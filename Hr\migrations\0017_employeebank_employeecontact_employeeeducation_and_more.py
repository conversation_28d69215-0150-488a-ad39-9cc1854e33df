# Generated by Django 5.0.14 on 2025-07-19 16:44

import Hr.models.employee.employee_bank_models
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Hr', '0016_add_attendance_models'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EmployeeBank',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف الفريد')),
                ('account_type', models.CharField(choices=[('salary', 'حساب الراتب'), ('savings', 'حساب توفير'), ('current', 'حساب جاري'), ('credit_card', 'بطاقة ائتمان'), ('loan', 'قرض'), ('other', 'أخرى')], default='salary', max_length=20, verbose_name='نوع الحساب')),
                ('is_primary', models.BooleanField(default=False, help_text='هل هذا هو الحساب الأساسي لتحويل الراتب؟', verbose_name='حساب أساسي')),
                ('bank_name', models.CharField(max_length=200, verbose_name='اسم البنك')),
                ('bank_code', models.CharField(blank=True, max_length=50, null=True, verbose_name='كود البنك')),
                ('branch_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='اسم الفرع')),
                ('branch_code', models.CharField(blank=True, max_length=50, null=True, verbose_name='كود الفرع')),
                ('account_number', Hr.models.employee.employee_bank_models.EncryptedField(max_length=200, verbose_name='رقم الحساب')),
                ('account_name', models.CharField(max_length=200, verbose_name='اسم الحساب')),
                ('iban', Hr.models.employee.employee_bank_models.EncryptedField(blank=True, max_length=200, null=True, verbose_name='رقم الآيبان (IBAN)')),
                ('swift_code', models.CharField(blank=True, max_length=20, null=True, verbose_name='رمز السويفت (SWIFT)')),
                ('currency', models.CharField(default='EGP', max_length=3, verbose_name='العملة')),
                ('card_number', Hr.models.employee.employee_bank_models.EncryptedField(blank=True, max_length=200, null=True, verbose_name='رقم البطاقة')),
                ('card_expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء البطاقة')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('pending', 'معلق'), ('closed', 'مغلق')], default='active', max_length=20, verbose_name='الحالة')),
                ('is_verified', models.BooleanField(default=False, verbose_name='تم التحقق')),
                ('verified_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التحقق')),
                ('account_document', models.FileField(blank=True, help_text='كشف حساب، خطاب من البنك، إلخ', null=True, upload_to='employee_bank_documents/', verbose_name='مستند الحساب')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'حساب بنكي للموظف',
                'verbose_name_plural': 'حسابات بنكية للموظفين',
                'db_table': 'hrms_employee_bank',
                'ordering': ['employee', '-is_primary', 'bank_name'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeContact',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف الفريد')),
                ('contact_type', models.CharField(choices=[('home', 'منزل'), ('work', 'عمل'), ('permanent', 'دائم'), ('temporary', 'مؤقت'), ('emergency', 'طوارئ'), ('other', 'آخر')], default='home', max_length=20, verbose_name='نوع جهة الاتصال')),
                ('is_primary', models.BooleanField(default=False, verbose_name='جهة اتصال أساسية')),
                ('address_line1', models.CharField(blank=True, max_length=200, null=True, verbose_name='العنوان - السطر 1')),
                ('address_line2', models.CharField(blank=True, max_length=200, null=True, verbose_name='العنوان - السطر 2')),
                ('city', models.CharField(blank=True, max_length=100, null=True, verbose_name='المدينة')),
                ('state', models.CharField(blank=True, max_length=100, null=True, verbose_name='المحافظة/الولاية')),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True, verbose_name='الرمز البريدي')),
                ('country', models.CharField(default='مصر', max_length=100, verbose_name='الدولة')),
                ('phone', models.CharField(blank=True, max_length=17, null=True, validators=[django.core.validators.RegexValidator(message="رقم الهاتف يجب أن يكون بصيغة: '+999999999'. يسمح بـ 15 رقم.", regex='^\\+?1?\\d{9,15}$')], verbose_name='رقم الهاتف')),
                ('mobile', models.CharField(blank=True, max_length=17, null=True, validators=[django.core.validators.RegexValidator(message="رقم الهاتف يجب أن يكون بصيغة: '+999999999'. يسمح بـ 15 رقم.", regex='^\\+?1?\\d{9,15}$')], verbose_name='رقم الجوال')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, validators=[django.core.validators.EmailValidator()], verbose_name='البريد الإلكتروني')),
                ('contact_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='اسم جهة الاتصال')),
                ('relationship', models.CharField(blank=True, max_length=50, null=True, verbose_name='صلة القرابة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'معلومات اتصال الموظف',
                'verbose_name_plural': 'معلومات اتصال الموظفين',
                'db_table': 'hrms_employee_contact',
                'ordering': ['employee', '-is_primary', 'contact_type'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeEducation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف الفريد')),
                ('education_type', models.CharField(choices=[('school', 'تعليم مدرسي'), ('diploma', 'دبلوم'), ('bachelor', 'بكالوريوس'), ('master', 'ماجستير'), ('phd', 'دكتوراه'), ('certification', 'شهادة مهنية'), ('course', 'دورة تدريبية'), ('other', 'أخرى')], max_length=20, verbose_name='نوع التعليم')),
                ('institution_name', models.CharField(max_length=200, verbose_name='اسم المؤسسة التعليمية')),
                ('institution_location', models.CharField(blank=True, max_length=200, null=True, verbose_name='موقع المؤسسة')),
                ('institution_country', models.CharField(blank=True, max_length=100, null=True, verbose_name='دولة المؤسسة')),
                ('degree_name', models.CharField(max_length=200, verbose_name='اسم الدرجة/المؤهل')),
                ('field_of_study', models.CharField(blank=True, max_length=200, null=True, verbose_name='مجال الدراسة')),
                ('specialization', models.CharField(blank=True, max_length=200, null=True, verbose_name='التخصص')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('is_current', models.BooleanField(default=False, verbose_name='حالي')),
                ('duration_months', models.PositiveIntegerField(blank=True, null=True, verbose_name='المدة (بالشهور)')),
                ('grade', models.CharField(blank=True, max_length=50, null=True, verbose_name='الدرجة/التقدير')),
                ('score', models.CharField(blank=True, max_length=50, null=True, verbose_name='الدرجة/المعدل')),
                ('score_type', models.CharField(blank=True, help_text='مثل: GPA، النسبة المئوية، إلخ', max_length=50, null=True, verbose_name='نوع الدرجة')),
                ('certificate_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='رقم الشهادة')),
                ('certificate_file', models.FileField(blank=True, null=True, upload_to='employee_certificates/', verbose_name='ملف الشهادة')),
                ('is_verified', models.BooleanField(default=False, verbose_name='تم التحقق')),
                ('verified_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التحقق')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف')),
                ('achievements', models.TextField(blank=True, null=True, verbose_name='الإنجازات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'تعليم الموظف',
                'verbose_name_plural': 'تعليم الموظفين',
                'db_table': 'hrms_employee_education',
                'ordering': ['employee', '-end_date', '-start_date'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeExperience',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف الفريد')),
                ('company_name', models.CharField(max_length=200, verbose_name='اسم الشركة')),
                ('company_location', models.CharField(blank=True, max_length=200, null=True, verbose_name='موقع الشركة')),
                ('company_country', models.CharField(blank=True, max_length=100, null=True, verbose_name='دولة الشركة')),
                ('company_industry', models.CharField(blank=True, max_length=100, null=True, verbose_name='مجال عمل الشركة')),
                ('job_title', models.CharField(max_length=200, verbose_name='المسمى الوظيفي')),
                ('department', models.CharField(blank=True, max_length=200, null=True, verbose_name='القسم')),
                ('employment_type', models.CharField(choices=[('full_time', 'دوام كامل'), ('part_time', 'دوام جزئي'), ('contract', 'تعاقد'), ('temporary', 'مؤقت'), ('intern', 'متدرب'), ('consultant', 'استشاري'), ('freelance', 'عمل حر'), ('volunteer', 'تطوع')], default='full_time', max_length=20, verbose_name='نوع التوظيف')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('is_current', models.BooleanField(default=False, verbose_name='حالي')),
                ('duration_months', models.PositiveIntegerField(blank=True, null=True, verbose_name='المدة (بالشهور)')),
                ('responsibilities', models.TextField(blank=True, null=True, verbose_name='المسؤوليات')),
                ('achievements', models.TextField(blank=True, null=True, verbose_name='الإنجازات')),
                ('skills_used', models.JSONField(default=list, help_text='قائمة بالمهارات المستخدمة في هذه الوظيفة', verbose_name='المهارات المستخدمة')),
                ('salary', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='الراتب')),
                ('currency', models.CharField(blank=True, default='EGP', max_length=3, null=True, verbose_name='العملة')),
                ('reference_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='اسم المرجع')),
                ('reference_contact', models.CharField(blank=True, max_length=200, null=True, verbose_name='معلومات الاتصال بالمرجع')),
                ('reference_position', models.CharField(blank=True, max_length=200, null=True, verbose_name='منصب المرجع')),
                ('is_verified', models.BooleanField(default=False, verbose_name='تم التحقق')),
                ('verified_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التحقق')),
                ('reason_for_leaving', models.TextField(blank=True, null=True, verbose_name='سبب ترك العمل')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('experience_certificate', models.FileField(blank=True, null=True, upload_to='employee_experience/', verbose_name='شهادة الخبرة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'خبرة الموظف',
                'verbose_name_plural': 'خبرات الموظفين',
                'db_table': 'hrms_employee_experience',
                'ordering': ['employee', '-is_current', '-end_date', '-start_date'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeFamily',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف الفريد')),
                ('relationship', models.CharField(choices=[('spouse', 'زوج/زوجة'), ('child', 'ابن/ابنة'), ('parent', 'أب/أم'), ('sibling', 'أخ/أخت'), ('grandparent', 'جد/جدة'), ('grandchild', 'حفيد/حفيدة'), ('other', 'أخرى')], max_length=20, verbose_name='صلة القرابة')),
                ('other_relationship', models.CharField(blank=True, help_text="يرجى تحديد صلة القرابة إذا اخترت 'أخرى'", max_length=100, null=True, verbose_name='صلة القرابة الأخرى')),
                ('first_name', models.CharField(max_length=100, verbose_name='الاسم الأول')),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='الاسم الأوسط')),
                ('last_name', models.CharField(max_length=100, verbose_name='اسم العائلة')),
                ('full_name', models.CharField(blank=True, max_length=300, null=True, verbose_name='الاسم الكامل')),
                ('gender', models.CharField(choices=[('male', 'ذكر'), ('female', 'أنثى')], max_length=10, verbose_name='الجنس')),
                ('birth_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد')),
                ('birth_place', models.CharField(blank=True, max_length=200, null=True, verbose_name='مكان الميلاد')),
                ('national_id', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهوية الوطنية')),
                ('phone', models.CharField(blank=True, max_length=17, null=True, validators=[django.core.validators.RegexValidator(message="رقم الهاتف يجب أن يكون بصيغة: '+999999999'. يسمح بـ 15 رقم.", regex='^\\+?1?\\d{9,15}$')], verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('is_dependent', models.BooleanField(default=False, help_text='هل هذا الشخص معال من قبل الموظف؟', verbose_name='معال')),
                ('is_emergency_contact', models.BooleanField(default=False, verbose_name='جهة اتصال في حالات الطوارئ')),
                ('is_beneficiary', models.BooleanField(default=False, help_text='هل هذا الشخص مستفيد من التأمين أو المعاشات؟', verbose_name='مستفيد')),
                ('is_covered_by_insurance', models.BooleanField(default=False, verbose_name='مشمول بالتأمين الصحي')),
                ('insurance_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='رقم التأمين')),
                ('education_level', models.CharField(blank=True, max_length=100, null=True, verbose_name='المستوى التعليمي')),
                ('occupation', models.CharField(blank=True, max_length=200, null=True, verbose_name='المهنة')),
                ('employer', models.CharField(blank=True, max_length=200, null=True, verbose_name='جهة العمل')),
                ('document_file', models.FileField(blank=True, help_text='شهادة ميلاد، عقد زواج، إلخ', null=True, upload_to='employee_family_documents/', verbose_name='ملف الوثيقة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'فرد من عائلة الموظف',
                'verbose_name_plural': 'أفراد عائلة الموظف',
                'db_table': 'hrms_employee_family',
                'ordering': ['employee', 'relationship', 'first_name'],
            },
        ),
        # COMMENTED OUT - EmployeeSalaryComponent depends on non-existent Hr.salarycomponent table
        # migrations.CreateModel(
        #     name='EmployeeSalaryComponent',
        #     fields=[
        #         ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف الفريد')),
        #         ('amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ')),
        #         ('override_calculation', models.BooleanField(default=False, verbose_name='تجاوز الحساب التلقائي')),
        #         ('custom_formula', models.TextField(blank=True, null=True, verbose_name='معادلة مخصصة')),
        #         ('conditions', models.JSONField(default=dict, help_text='شروط خاصة لهذا المكون', verbose_name='الشروط')),
        #         ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
        #         ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
        #         ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
        #     ],
        #     options={
        #         'verbose_name': 'مكون راتب موظف',
        #         'verbose_name_plural': 'مكونات رواتب الموظفين',
        #         'db_table': 'hrms_employee_salary_component',
        #         'ordering': ['salary_component__display_order'],
        #     },
        # ),
        migrations.CreateModel(
            name='EmployeeSalaryStructure',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف الفريد')),
                ('structure_name', models.CharField(max_length=200, verbose_name='اسم هيكل الراتب')),
                ('structure_code', models.CharField(max_length=20, verbose_name='كود هيكل الراتب')),
                ('effective_from', models.DateField(verbose_name='ساري من')),
                ('effective_to', models.DateField(blank=True, null=True, verbose_name='ساري حتى')),
                ('basic_salary', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='الراتب الأساسي')),
                ('currency', models.CharField(default='EGP', max_length=3, verbose_name='العملة')),
                ('total_earnings', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='إجمالي الاستحقاقات')),
                ('total_deductions', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='إجمالي الخصومات')),
                ('net_salary', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='صافي الراتب')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_current', models.BooleanField(default=False, verbose_name='الهيكل الحالي')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'هيكل راتب موظف',
                'verbose_name_plural': 'هياكل رواتب الموظفين',
                'db_table': 'hrms_employee_salary_structure',
                'ordering': ['-effective_from'],
            },
        ),
        migrations.CreateModel(
            name='HrJob',
            fields=[
                ('jop_code', models.IntegerField(db_column='Jop_Code', primary_key=True, serialize=False, verbose_name='رمز الوظيفة')),
                ('jop_name', models.CharField(db_column='Jop_Name', max_length=50, verbose_name='اسم الوظيفة')),
            ],
            options={
                'verbose_name': 'الوظيفة (قديم)',
                'verbose_name_plural': 'الوظائف (قديمة)',
                'db_table': 'Tbl_Jop',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='JobLevel',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف الفريد')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المستوى الوظيفي')),
                ('name_en', models.CharField(blank=True, max_length=100, null=True, verbose_name='الاسم بالإنجليزية')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المستوى')),
                ('level_order', models.PositiveIntegerField(default=0, help_text='ترتيب المستوى في الهيكل التنظيمي (الأقل هو الأعلى)', verbose_name='ترتيب المستوى')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف المستوى')),
                ('min_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='الحد الأدنى للراتب')),
                ('max_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='الحد الأقصى للراتب')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مستوى وظيفي',
                'verbose_name_plural': 'مستويات وظيفية',
                'ordering': ['level_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='LegacyPayrollEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='إجمالي المبلغ')),
                ('status', models.CharField(max_length=50, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'قيد الراتب (قديم)',
                'verbose_name_plural': 'قيود الرواتب (قديمة)',
                'db_table': 'Tbl_Payroll_Entry',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='LegacyPayrollPeriod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('period', models.CharField(max_length=50, verbose_name='الفترة')),
                ('status', models.CharField(max_length=50, verbose_name='الحالة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي المبلغ')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'فترة الرواتب القديمة',
                'verbose_name_plural': 'فترات الرواتب القديمة',
                'db_table': 'Tbl_Payroll_Period',
                'managed': True,
            },
        ),
        # COMMENTED OUT - PayrollDetail depends on non-existent Hr.salarycomponent table
        # migrations.CreateModel(
        #     name='PayrollDetail',
        #     fields=[
        #         ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف الفريد')),
        #         ('amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ')),
        #         ('is_prorated', models.BooleanField(default=False, verbose_name='محسوب نسبياً')),
        #         ('proration_factor', models.DecimalField(decimal_places=4, default=1.0, max_digits=5, verbose_name='معامل الحساب النسبي')),
        #         ('original_amount', models.DecimalField(blank=True, decimal_places=2, help_text='المبلغ قبل الحساب النسبي', max_digits=12, null=True, verbose_name='المبلغ الأصلي')),
        #         ('calculation_basis', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='أساس الحساب')),
        #         ('calculation_method', models.CharField(blank=True, max_length=20, null=True, verbose_name='طريقة الحساب')),
        #         ('formula_used', models.TextField(blank=True, null=True, verbose_name='المعادلة المستخدمة')),
        #         ('is_manually_adjusted', models.BooleanField(default=False, verbose_name='معدل يدوياً')),
        #         ('adjustment_reason', models.CharField(blank=True, max_length=200, null=True, verbose_name='سبب التعديل')),
        #         ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
        #         ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
        #     ],
        #     options={
        #         'verbose_name': 'تفاصيل الراتب',
        #         'verbose_name_plural': 'تفاصيل الرواتب',
        #         'db_table': 'hrms_payroll_detail',
        #         'ordering': ['salary_component__display_order'],
        #     },
        # ),
        # COMMENTED OUT - PayrollDetailHistory depends on PayrollDetail which was commented out
        # migrations.CreateModel(
        #     name='PayrollDetailHistory',
        #     fields=[
        #         ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف الفريد')),
        #         ('previous_amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ السابق')),
        #         ('new_amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ الجديد')),
        #         ('change_reason', models.CharField(max_length=200, verbose_name='سبب التغيير')),
        #         ('changed_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التغيير')),
        #     ],
        #     options={
        #         'verbose_name': 'سجل تغييرات تفاصيل الراتب',
        #         'verbose_name_plural': 'سجلات تغييرات تفاصيل الرواتب',
        #         'db_table': 'hrms_payroll_detail_history',
        #         'ordering': ['-changed_at'],
        #     },
        # ),
        # COMMENTED OUT - Table hrms_employee_emergency_contact does not exist
        # COMMENTED OUT - Model "employeeemergencycontact" table does not exist
        # # migrations.AlterUniqueTogether(
        # #     name='employeeemergencycontact',
        # #     unique_together=None,
        # # ),
        # COMMENTED OUT - Table hrms_employee_emergency_contact does not exist
        # COMMENTED OUT - Model "employeeemergencycontact" table does not exist
        # # migrations.RemoveField(
        # #     model_name='employeeemergencycontact',
        # #     name='created_by',
        # # ),
        # COMMENTED OUT - Model "employeeemergencycontact" table does not exist
        # # migrations.RemoveField(
        # #     model_name='employeeemergencycontact',
        # #     name='employee',
        # # ),
        # COMMENTED OUT - Table hrms_employee_training does not exist
        # COMMENTED OUT - Model "employeetraining" table does not exist
        # # migrations.RemoveField(
        # #     model_name='employeetraining',
        # #     name='approved_by',
        # # ),
        # COMMENTED OUT - Model "employeetraining" table does not exist
        # # migrations.RemoveField(
        # #     model_name='employeetraining',
        # #     name='created_by',
        # # ),
        # COMMENTED OUT - Model "employeetraining" table does not exist
        # # migrations.RemoveField(
        # #     model_name='employeetraining',
        # #     name='employee',
        # # ),
        # COMMENTED OUT - Model "hrattendancerecord" table does not exist
        # migrations.RemoveField(
        # model_name='hrattendancerecord',
        # name='machine',
        # ),
        # COMMENTED OUT - Model "hrattendancerecord" table does not exist
        # migrations.RemoveField(
        # model_name='hrattendancerecord',
        # name='employee',
        # ),
        # COMMENTED OUT - Model "hremployeeattendancerule" table does not exist
        # migrations.RemoveField(
        # model_name='hremployeeattendancerule',
        # name='attendance_rule',
        # ),
        # COMMENTED OUT - Table hrattendancesummary does not exist (table is Hr_AttendanceSummary)
        # COMMENTED OUT - Model "hrattendancesummary" table does not exist
        # # migrations.AlterUniqueTogether(
        # #     name='hrattendancesummary',
        # #     unique_together=None,
        # # ),
        # COMMENTED OUT - Table hrattendancesummary does not exist (table is Hr_AttendanceSummary)
        # COMMENTED OUT - Model "hrattendancesummary" table does not exist
        # # migrations.RemoveField(
        # #     model_name='hrattendancesummary',
        # #     name='employee',
        # # ),
        # COMMENTED OUT - Table hrpickuppoint does not exist (table is Hr_PickupPoint)
        # COMMENTED OUT - Model "hrpickuppoint" table does not exist
        # # migrations.RemoveField(
        # #     model_name='hrpickuppoint',
        # #     name='car',
        # # ),
        # COMMENTED OUT - Table hremployeeattendancerule does not exist (table is Hr_EmployeeAttendanceRule)
        # COMMENTED OUT - Model "hremployeeattendancerule" table does not exist
        # # migrations.RemoveField(
        # #     model_name='hremployeeattendancerule',
        # #     name='employee',
        # # ),
        # COMMENTED OUT - Table naming mismatch (actual table is Hr_employeenote, not Hr_hremployeenote)
        # COMMENTED OUT - Model "hremployeenote" table does not exist
        # # migrations.RemoveField(
        # #     model_name='hremployeenote',
        # #     name='created_by',
        # # ),
        # COMMENTED OUT - Model "hremployeenote" table does not exist
        # # migrations.RemoveField(
        # #     model_name='hremployeenote',
        # #     name='employee',
        # # ),
        # COMMENTED OUT - Model "hremployeenote" table does not exist
        # # migrations.RemoveField(
        # #     model_name='hremployeenote',
        # #     name='last_modified_by',
        # # ),
        # COMMENTED OUT - Model "hremployeenotehistory" table does not exist
        # # migrations.RemoveField(
        # #     model_name='hremployeenotehistory',
        # #     name='note',
        # # ),
        # COMMENTED OUT - Model "hremployeenotehistory" table does not exist
        # # migrations.RemoveField(
        # #     model_name='hremployeenotehistory',
        # #     name='changed_by',
        # # ),
        # COMMENTED OUT - Table naming mismatch (actual table is Hr_employeetask, not Hr_hremployeetask)
        # COMMENTED OUT - Model "hremployeetask" table does not exist
        # # migrations.RemoveField(
        # #     model_name='hremployeetask',
        # #     name='assigned_by',
        # # ),
        # COMMENTED OUT - Model "hremployeetask" table does not exist
        # # migrations.RemoveField(
        # #     model_name='hremployeetask',
        # #     name='employee',
        # # ),
        # COMMENTED OUT - Model "hrofficialholiday" table does not exist
        # migrations.DeleteModel(
        # name='HrOfficialHoliday',
        # ),
        # COMMENTED OUT - Model "hrtasknew" table does not exist
        # migrations.RemoveField(
        # model_name='hrtasknew',
        # name='assigned_to',
        # ),
        # COMMENTED OUT - Model "hrtasknew" table does not exist
        # migrations.RemoveField(
        # model_name='hrtasknew',
        # name='created_by',
        # ),
        migrations.RemoveField(
            model_name='taskstep',
            name='created_by',
        ),
        # COMMENTED OUT - Model "legacyemployee" table does not exist
        # migrations.DeleteModel(
        # name='LegacyEmployee',
        # ),
        migrations.AlterModelOptions(
            name='payrollentry',
            options={'ordering': ['payroll_period', 'employee__employee_number'], 'verbose_name': 'قيد راتب', 'verbose_name_plural': 'قيود الرواتب'},
        ),
        migrations.AlterModelOptions(
            name='payrollperiod',
            options={'ordering': ['-start_date', '-created_at'], 'verbose_name': 'فترة راتب', 'verbose_name_plural': 'فترات الرواتب'},
        ),
        # MOVED - AlterUniqueTogether for payrollentry moved after payroll_period field is added
        # migrations.AlterUniqueTogether(
        #     name='payrollentry',
        #     unique_together={('payroll_period', 'employee')},
        # ),
        # MOVED - AlterUniqueTogether for payrollperiod moved after start_date, end_date, period_type fields are added
        # migrations.AlterUniqueTogether(
        #     name='payrollperiod',
        #     unique_together={('start_date', 'end_date', 'period_type')},
        # ),
        migrations.AddField(
            model_name='payrollentry',
            name='absent_days',
            field=models.DecimalField(decimal_places=1, default=0, max_digits=5, verbose_name='أيام الغياب'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_payroll_entries', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='bank_account',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='الحساب البنكي'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='basic_salary',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='الراتب الأساسي'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='calculation_notes',
            field=models.TextField(blank=True, null=True, verbose_name='ملاحظات الحساب'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_payroll_entries', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='is_prorated',
            field=models.BooleanField(default=False, verbose_name='محسوب نسبياً'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='leave_days',
            field=models.DecimalField(decimal_places=1, default=0, max_digits=5, verbose_name='أيام الإجازة'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='net_salary',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='صافي الراتب'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='overtime_hours',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=6, verbose_name='ساعات العمل الإضافي'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='payment_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ الدفع'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='payment_method',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='طريقة الدفع'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='payment_reference',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='مرجع الدفع'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='payroll_period',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='payroll_entries', to='Hr.payrollperiod', verbose_name='فترة الراتب'),
            preserve_default=False,
        ),
        # COMMENTED OUT - AlterUniqueTogether for payrollentry causes column name mismatch
        # The existing table has employee_id and period_id, but migration expects payroll_period and employee
        # migrations.AlterUniqueTogether(
        #     name='payrollentry',
        #     unique_together={('payroll_period', 'employee')},
        # ),
        migrations.AddField(
            model_name='payrollentry',
            name='present_days',
            field=models.DecimalField(decimal_places=1, default=0, max_digits=5, verbose_name='أيام الحضور'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='proration_factor',
            field=models.DecimalField(decimal_places=4, default=1.0, max_digits=5, verbose_name='معامل الحساب النسبي'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='reviewed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_payroll_entries', to=settings.AUTH_USER_MODEL, verbose_name='تمت المراجعة بواسطة'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='total_deductions',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='إجمالي الخصومات'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='total_earnings',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='إجمالي الاستحقاقات'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='working_days',
            field=models.DecimalField(decimal_places=1, default=0, max_digits=5, verbose_name='أيام العمل'),
        ),
        migrations.AddField(
            model_name='payrollperiod',
            name='end_date',
            field=models.DateField(default=django.utils.timezone.now, help_text='تاريخ نهاية فترة الراتب', verbose_name='تاريخ النهاية'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='payrollperiod',
            name='is_active',
            field=models.BooleanField(default=True, help_text='هل هذه الفترة نشطة؟', verbose_name='نشط'),
        ),
        migrations.AddField(
            model_name='payrollperiod',
            name='name',
            field=models.CharField(default='فترة راتب', help_text='مثال: راتب يناير 2024', max_length=100, verbose_name='اسم الفترة'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='payrollperiod',
            name='name_en',
            field=models.CharField(blank=True, help_text='January 2024 Payroll', max_length=100, verbose_name='الاسم بالإنجليزية'),
        ),
        migrations.AddField(
            model_name='payrollperiod',
            name='notes',
            field=models.TextField(blank=True, help_text='ملاحظات إضافية حول فترة الراتب', verbose_name='ملاحظات'),
        ),
        migrations.AddField(
            model_name='payrollperiod',
            name='pay_date',
            field=models.DateField(default=django.utils.timezone.now, help_text='التاريخ المخطط لدفع الرواتب', verbose_name='تاريخ الدفع'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='payrollperiod',
            name='period_type',
            field=models.CharField(choices=[('monthly', 'شهري'), ('bi_weekly', 'كل أسبوعين'), ('weekly', 'أسبوعي'), ('quarterly', 'ربع سنوي'), ('annual', 'سنوي')], default='monthly', max_length=20, verbose_name='نوع الفترة'),
        ),
        migrations.AddField(
            model_name='payrollperiod',
            name='processed_at',
            field=models.DateTimeField(blank=True, help_text='تاريخ ووقت معالجة الرواتب', null=True, verbose_name='تاريخ المعالجة'),
        ),
        migrations.AddField(
            model_name='payrollperiod',
            name='start_date',
            field=models.DateField(default=django.utils.timezone.now, help_text='تاريخ بداية فترة الراتب', verbose_name='تاريخ البداية'),
            preserve_default=False,
        ),
        # MOVED HERE - AlterUniqueTogether for payrollperiod after all required fields are added
        migrations.AlterUniqueTogether(
            name='payrollperiod',
            unique_together={('start_date', 'end_date', 'period_type')},
        ),
        migrations.AddField(
            model_name='payrollperiod',
            name='total_allowances',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='مجموع جميع البدلات', max_digits=15, verbose_name='إجمالي البدلات'),
        ),
        migrations.AddField(
            model_name='payrollperiod',
            name='total_deductions',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='مجموع جميع الخصومات', max_digits=15, verbose_name='إجمالي الخصومات'),
        ),
        migrations.AddField(
            model_name='payrollperiod',
            name='total_employees',
            field=models.PositiveIntegerField(default=0, help_text='عدد الموظفين في هذه الفترة', verbose_name='إجمالي الموظفين'),
        ),
        migrations.AddField(
            model_name='payrollperiod',
            name='total_gross_salary',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='مجموع الرواتب الأساسية لجميع الموظفين', max_digits=15, verbose_name='إجمالي الراتب الأساسي'),
        ),
        migrations.AddField(
            model_name='payrollperiod',
            name='total_net_salary',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='مجموع الرواتب الصافية لجميع الموظفين', max_digits=15, verbose_name='إجمالي الراتب الصافي'),
        ),
        migrations.AlterField(
            model_name='payrollentry',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='payroll_entries', to='Hr.employee', verbose_name='الموظف'),
        ),
        # COMMENTED OUT - MSSQL backend doesn't support altering from BigAutoField to UUIDField
        # migrations.AlterField(
        #     model_name='payrollentry',
        #     name='id',
        #     field=models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف الفريد'),
        # ),
        migrations.AlterField(
            model_name='payrollentry',
            name='status',
            field=models.CharField(choices=[('draft', 'مسودة'), ('calculated', 'محسوب'), ('reviewed', 'تمت المراجعة'), ('approved', 'معتمد'), ('paid', 'مدفوع'), ('cancelled', 'ملغي')], default='draft', max_length=20, verbose_name='الحالة'),
        ),
        migrations.AlterField(
            model_name='payrollperiod',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='approved_payroll_periods', to=settings.AUTH_USER_MODEL, verbose_name='وافق عليه'),
        ),
        # COMMENTED OUT - created_by_id field already exists with foreign key constraint
        # MSSQL doesn't allow default constraint on existing NOT NULL foreign key field
        # migrations.AlterField(
        #     model_name='payrollperiod',
        #     name='created_by',
        #     field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.PROTECT, related_name='created_payroll_periods', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        #     preserve_default=False,
        # ),
        # COMMENTED OUT - MSSQL backend doesn't support altering from BigAutoField to UUIDField
        # migrations.AlterField(
        #     model_name='payrollperiod',
        #     name='id',
        #     field=models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف'),
        # ),
        migrations.AlterField(
            model_name='payrollperiod',
            name='status',
            field=models.CharField(choices=[('draft', 'مسودة'), ('active', 'نشط'), ('processing', 'قيد المعالجة'), ('completed', 'مكتمل'), ('closed', 'مغلق'), ('cancelled', 'ملغي')], default='draft', max_length=20, verbose_name='الحالة'),
        ),
        # COMMENTED OUT - Index creation on fields that may not exist or be added properly
        # Some fields like start_date, end_date, period_type, is_active may not exist in current table
        # migrations.AddIndex(
        #     model_name='payrollperiod',
        #     index=models.Index(fields=['start_date', 'end_date'], name='Hr_payrollp_start_d_1ff43f_idx'),
        # ),
        # migrations.AddIndex(
        #     model_name='payrollperiod',
        #     index=models.Index(fields=['status'], name='Hr_payrollp_status_6601ac_idx'),
        # ),
        # migrations.AddIndex(
        #     model_name='payrollperiod',
        #     index=models.Index(fields=['period_type'], name='Hr_payrollp_period__2cc7ee_idx'),
        # ),
        # migrations.AddIndex(
        #     model_name='payrollperiod',
        #     index=models.Index(fields=['is_active'], name='Hr_payrollp_is_acti_3ea6db_idx'),
        # ),
        # COMMENTED OUT - Table renaming conflicts with existing tables
        # Hr_PayrollEntry and Hr_PayrollPeriod already exist
        # migrations.AlterModelTable(
        #     name='payrollentry',
        #     table='hrms_payroll_entry',
        # ),
        # migrations.AlterModelTable(
        #     name='payrollperiod',
        #     table=None,
        # ),
        migrations.AddField(
            model_name='employeebank',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_employee_bank_accounts', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='employeebank',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bank_accounts', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='employeebank',
            name='verified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_bank_accounts', to=settings.AUTH_USER_MODEL, verbose_name='تم التحقق بواسطة'),
        ),
        migrations.AddField(
            model_name='employeecontact',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_employee_contacts', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='employeecontact',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contact_details', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='employeeeducation',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_employee_education', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='employeeeducation',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='education', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='employeeeducation',
            name='verified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_education', to=settings.AUTH_USER_MODEL, verbose_name='تم التحقق بواسطة'),
        ),
        migrations.AddField(
            model_name='employeeexperience',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_employee_experience', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='employeeexperience',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='experience', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='employeeexperience',
            name='verified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_experience', to=settings.AUTH_USER_MODEL, verbose_name='تم التحقق بواسطة'),
        ),
        migrations.AddField(
            model_name='employeefamily',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_employee_family', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='employeefamily',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='family_members', to='Hr.employee', verbose_name='الموظف'),
        ),
        # COMMENTED OUT - EmployeeSalaryComponent model was commented out
        # migrations.AddField(
        #     model_name='employeesalarycomponent',
        #     name='salary_component',
        #     field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_assignments', to='Hr.salarycomponent', verbose_name='مكون الراتب'),
        # ),
        migrations.AddField(
            model_name='employeesalarystructure',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_salary_structures', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة'),
        ),
        migrations.AddField(
            model_name='employeesalarystructure',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_salary_structures', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='employeesalarystructure',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='salary_structures', to='Hr.employee', verbose_name='الموظف'),
        ),
        # COMMENTED OUT - EmployeeSalaryComponent model was commented out
        # migrations.AddField(
        #     model_name='employeesalarycomponent',
        #     name='salary_structure',
        #     field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='salary_components', to='Hr.employeesalarystructure', verbose_name='هيكل الراتب'),
        # ),
        migrations.AddField(
            model_name='payrollentry',
            name='salary_structure',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.PROTECT, related_name='payroll_entries', to='Hr.employeesalarystructure', verbose_name='هيكل الراتب'),
            preserve_default=False,
        ),
        # COMMENTED OUT - Index creation on fields that don't exist in current table structure
        # Current table has employee_id, period_id, but migration expects payroll_period, employee
        # migrations.AddIndex(
        #     model_name='payrollentry',
        #     index=models.Index(fields=['payroll_period', 'employee'], name='hrms_payrol_payroll_4e901d_idx'),
        # ),
        # migrations.AddIndex(
        #     model_name='payrollentry',
        #     index=models.Index(fields=['status'], name='hrms_payrol_status_77cc30_idx'),
        # ),
        # migrations.AddIndex(
        #     model_name='payrollentry',
        #     index=models.Index(fields=['payment_date'], name='hrms_payrol_payment_53b6ef_idx'),
        # ),
        migrations.AddField(
            model_name='hrjob',
            name='department',
            field=models.ForeignKey(blank=True, db_column='Dept_Code', null=True, on_delete=django.db.models.deletion.SET_NULL, to='Hr.legacydepartment', verbose_name='القسم'),
        ),
        migrations.AddIndex(
            model_name='joblevel',
            index=models.Index(fields=['code'], name='Hr_joblevel_code_36b106_idx'),
        ),
        migrations.AddIndex(
            model_name='joblevel',
            index=models.Index(fields=['level_order'], name='Hr_joblevel_level_o_35459a_idx'),
        ),
        migrations.AddIndex(
            model_name='joblevel',
            index=models.Index(fields=['is_active'], name='Hr_joblevel_is_acti_634c6c_idx'),
        ),
        migrations.AddField(
            model_name='legacypayrollentry',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AlterField(
            model_name='payrollitemdetail',
            name='payroll_entry',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.legacypayrollentry', verbose_name='قيد الراتب'),
        ),
        migrations.AddField(
            model_name='legacypayrollperiod',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_legacy_payroll_periods', to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة'),
        ),
        migrations.AddField(
            model_name='legacypayrollperiod',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_legacy_payroll_periods', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='legacypayrollentry',
            name='period',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.legacypayrollperiod', verbose_name='الفترة'),
        ),
        # COMMENTED OUT - PayrollDetail and PayrollDetailHistory models were commented out
        # migrations.AddField(
        #     model_name='payrolldetail',
        #     name='adjusted_by',
        #     field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='adjusted_payroll_details', to=settings.AUTH_USER_MODEL, verbose_name='عدل بواسطة'),
        # ),
        # migrations.AddField(
        #     model_name='payrolldetail',
        #     name='payroll_entry',
        #     field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payroll_details', to='Hr.payrollentry', verbose_name='قيد الراتب'),
        # ),
        # migrations.AddField(
        #     model_name='payrolldetail',
        #     name='salary_component',
        #     field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='payroll_details', to='Hr.salarycomponent', verbose_name='مكون الراتب'),
        # ),
        # migrations.AddField(
        #     model_name='payrolldetailhistory',
        #     name='changed_by',
        #     field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payroll_detail_changes', to=settings.AUTH_USER_MODEL, verbose_name='غير بواسطة'),
        # ),
        # migrations.AddField(
        #     model_name='payrolldetailhistory',
        #     name='payroll_detail',
        #     field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='Hr.payrolldetail', verbose_name='تفاصيل الراتب'),
        # ),
        # COMMENTED OUT - Model "employeeemergencycontact" table does not exist
        # migrations.DeleteModel(
        # name='EmployeeEmergencyContact',
        # ),
        # COMMENTED OUT - Model "employeetraining" table does not exist
        # migrations.DeleteModel(
        # name='EmployeeTraining',
        # ),
        migrations.DeleteModel(
            name='HrAttendanceMachine',
        ),
        # COMMENTED OUT - Model "hrattendancerecord" table does not exist
        # migrations.DeleteModel(
        # name='HrAttendanceRecord',
        # ),
        migrations.DeleteModel(
            name='HrAttendanceRule',
        ),
        # COMMENTED OUT - Model "hrattendancesummary" table does not exist
        # migrations.DeleteModel(
        # name='HrAttendanceSummary',
        # ),
        # COMMENTED OUT - Table Hr_Car does not exist
        # migrations.DeleteModel(
        #     name='HrCar',
        # ),
        # COMMENTED OUT - Model "hrpickuppoint" table does not exist
        # migrations.DeleteModel(
        # name='HrPickupPoint',
        # ),
        # COMMENTED OUT - Model "hremployeeattendancerule" table does not exist
        # migrations.DeleteModel(
        # name='HrEmployeeAttendanceRule',
        # ),
        # COMMENTED OUT - Model "hremployeenote" table does not exist
        # migrations.DeleteModel(
        # name='HrEmployeeNote',
        # ),
        # COMMENTED OUT - Model "hremployeenotehistory" table does not exist
        # migrations.DeleteModel(
        # name='HrEmployeeNoteHistory',
        # ),
        # COMMENTED OUT - Model "hremployeetask" table does not exist
        # migrations.DeleteModel(
        # name='HrEmployeeTask',
        # ),
        # COMMENTED OUT - Model "hrtasknew" table does not exist
        # migrations.DeleteModel(
        # name='HrTaskNew',
        # ),
        migrations.DeleteModel(
            name='TaskStep',
        ),
        # COMMENTED OUT - Field name mismatch (database has period_id, not period)
        # migrations.RemoveField(
        #     model_name='payrollentry',
        #     name='period',
        # ),
        # migrations.RemoveField(
        #     model_name='payrollentry',
        #     name='total_amount',
        # ),
        # migrations.RemoveField(
        #     model_name='payrollperiod',
        #     name='period',
        # ),
        # migrations.RemoveField(
        #     model_name='payrollperiod',
        #     name='total_amount',
        # ),
        migrations.AddIndex(
            model_name='employeebank',
            index=models.Index(fields=['employee', 'account_type'], name='hrms_employ_employe_68916b_idx'),
        ),
        migrations.AddIndex(
            model_name='employeebank',
            index=models.Index(fields=['is_primary'], name='hrms_employ_is_prim_f3695f_idx'),
        ),
        migrations.AddIndex(
            model_name='employeebank',
            index=models.Index(fields=['status'], name='hrms_employ_status_ce0e5f_idx'),
        ),
        migrations.AddIndex(
            model_name='employeebank',
            index=models.Index(fields=['is_verified'], name='hrms_employ_is_veri_767500_idx'),
        ),
        migrations.AddIndex(
            model_name='employeecontact',
            index=models.Index(fields=['employee', 'contact_type'], name='hrms_employ_employe_3d6e08_idx'),
        ),
        migrations.AddIndex(
            model_name='employeecontact',
            index=models.Index(fields=['is_primary'], name='hrms_employ_is_prim_46a46f_idx'),
        ),
        migrations.AddIndex(
            model_name='employeeeducation',
            index=models.Index(fields=['employee', 'education_type'], name='hrms_employ_employe_847cca_idx'),
        ),
        migrations.AddIndex(
            model_name='employeeeducation',
            index=models.Index(fields=['is_verified'], name='hrms_employ_is_veri_1ab34c_idx'),
        ),
        migrations.AddIndex(
            model_name='employeeexperience',
            index=models.Index(fields=['employee'], name='hrms_employ_employe_cfb37a_idx'),
        ),
        migrations.AddIndex(
            model_name='employeeexperience',
            index=models.Index(fields=['is_current'], name='hrms_employ_is_curr_b86a6d_idx'),
        ),
        migrations.AddIndex(
            model_name='employeeexperience',
            index=models.Index(fields=['is_verified'], name='hrms_employ_is_veri_d7b4f8_idx'),
        ),
        migrations.AddIndex(
            model_name='employeefamily',
            index=models.Index(fields=['employee', 'relationship'], name='hrms_employ_employe_748dd3_idx'),
        ),
        migrations.AddIndex(
            model_name='employeefamily',
            index=models.Index(fields=['is_dependent'], name='hrms_employ_is_depe_262813_idx'),
        ),
        migrations.AddIndex(
            model_name='employeefamily',
            index=models.Index(fields=['is_emergency_contact'], name='hrms_employ_is_emer_1c1b62_idx'),
        ),
        migrations.AddIndex(
            model_name='employeefamily',
            index=models.Index(fields=['is_beneficiary'], name='hrms_employ_is_bene_7af4c5_idx'),
        ),
        migrations.AddIndex(
            model_name='employeesalarystructure',
            index=models.Index(fields=['employee', 'effective_from'], name='hrms_employ_employe_89f358_idx'),
        ),
        migrations.AddIndex(
            model_name='employeesalarystructure',
            index=models.Index(fields=['is_active'], name='hrms_employ_is_acti_b7d5da_idx'),
        ),
        migrations.AddIndex(
            model_name='employeesalarystructure',
            index=models.Index(fields=['is_current'], name='hrms_employ_is_curr_c7e44f_idx'),
        ),
        # COMMENTED OUT - EmployeeSalaryComponent model was commented out
        # migrations.AddIndex(
        #     model_name='employeesalarycomponent',
        #     index=models.Index(fields=['salary_structure'], name='hrms_employ_salary__683f72_idx'),
        # ),
        # migrations.AddIndex(
        #     model_name='employeesalarycomponent',
        #     index=models.Index(fields=['salary_component'], name='hrms_employ_salary__a29c51_idx'),
        # ),
        # migrations.AddIndex(
        #     model_name='employeesalarycomponent',
        #     index=models.Index(fields=['is_active'], name='hrms_employ_is_acti_e3cee7_idx'),
        # ),
        # migrations.AlterUniqueTogether(
        #     name='employeesalarycomponent',
        #     unique_together={('salary_structure', 'salary_component')},
        # ),
        # COMMENTED OUT - PayrollDetail model was commented out
        # migrations.AddIndex(
        #     model_name='payrolldetail',
        #     index=models.Index(fields=['payroll_entry'], name='hrms_payrol_payroll_293638_idx'),
        # ),
        # migrations.AddIndex(
        #     model_name='payrolldetail',
        #     index=models.Index(fields=['salary_component'], name='hrms_payrol_salary__0465b5_idx'),
        # ),
        # migrations.AlterUniqueTogether(
        #     name='payrolldetail',
        #     unique_together={('payroll_entry', 'salary_component')},
        # ),
    ]
