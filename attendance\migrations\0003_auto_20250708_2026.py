# Generated by Django 5.0.14 on 2025-07-08 17:26
# Drop all foreign key constraints to Tbl_Employee

from django.db import migrations


def drop_all_employee_fk_constraints(apps, schema_editor):
    """Drop all foreign key constraints that reference Tbl_Employee"""
    with schema_editor.connection.cursor() as cursor:
        cursor.execute("""
            DECLARE @sql NVARCHAR(MAX) = ''
            SELECT @sql = @sql + 'ALTER TABLE ' + QUOTENAME(OBJECT_SCHEMA_NAME(fk.parent_object_id)) + '.' + QUOTENAME(OBJECT_NAME(fk.parent_object_id)) + ' DROP CONSTRAINT ' + QUOTENAME(fk.name) + '; '
            FROM sys.foreign_keys fk
            INNER JOIN sys.tables tr ON fk.referenced_object_id = tr.object_id
            WHERE tr.name = 'Tbl_Employee'

            IF @sql <> ''
            BEGIN
                PRINT 'Dropping foreign key constraints to Tbl_Employee:'
                PRINT @sql
                EXEC sp_executesql @sql
            END
        """)


def reverse_drop_constraints(apps, schema_editor):
    """This will be handled by a later migration after Hr models are updated"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('attendance', '0002_auto_20250708_2024'),
    ]

    operations = [
        migrations.RunPython(
            drop_all_employee_fk_constraints,
            reverse_drop_constraints,
        ),
    ]
