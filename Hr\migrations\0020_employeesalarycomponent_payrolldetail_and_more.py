# Generated by Django 5.0.14 on 2025-07-20 14:14

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Hr', '0019_alter_hrjob_table'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EmployeeSalaryComponent',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف الفريد')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ')),
                ('override_calculation', models.BooleanField(default=False, verbose_name='تجاوز الحساب التلقائي')),
                ('custom_formula', models.TextField(blank=True, null=True, verbose_name='معادلة مخصصة')),
                ('conditions', models.JSONField(default=dict, help_text='شروط خاصة لهذا المكون', verbose_name='الشروط')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مكون راتب موظف',
                'verbose_name_plural': 'مكونات رواتب الموظفين',
                'db_table': 'hrms_employee_salary_component',
                'ordering': ['salary_component__display_order'],
            },
        ),
        migrations.CreateModel(
            name='PayrollDetail',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف الفريد')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ')),
                ('is_prorated', models.BooleanField(default=False, verbose_name='محسوب نسبياً')),
                ('proration_factor', models.DecimalField(decimal_places=4, default=1.0, max_digits=5, verbose_name='معامل الحساب النسبي')),
                ('original_amount', models.DecimalField(blank=True, decimal_places=2, help_text='المبلغ قبل الحساب النسبي', max_digits=12, null=True, verbose_name='المبلغ الأصلي')),
                ('calculation_basis', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='أساس الحساب')),
                ('calculation_method', models.CharField(blank=True, max_length=20, null=True, verbose_name='طريقة الحساب')),
                ('formula_used', models.TextField(blank=True, null=True, verbose_name='المعادلة المستخدمة')),
                ('is_manually_adjusted', models.BooleanField(default=False, verbose_name='معدل يدوياً')),
                ('adjustment_reason', models.CharField(blank=True, max_length=200, null=True, verbose_name='سبب التعديل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'تفاصيل الراتب',
                'verbose_name_plural': 'تفاصيل الرواتب',
                'db_table': 'hrms_payroll_detail',
                'ordering': ['salary_component__display_order'],
            },
        ),
        migrations.CreateModel(
            name='PayrollDetailHistory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف الفريد')),
                ('previous_amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ السابق')),
                ('new_amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ الجديد')),
                ('change_reason', models.CharField(max_length=200, verbose_name='سبب التغيير')),
                ('changed_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التغيير')),
            ],
            options={
                'verbose_name': 'سجل تغييرات تفاصيل الراتب',
                'verbose_name_plural': 'سجلات تغييرات تفاصيل الرواتب',
                'db_table': 'hrms_payroll_detail_history',
                'ordering': ['-changed_at'],
            },
        ),
        migrations.AlterUniqueTogether(
            name='employeeemergencycontact',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='employeeemergencycontact',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='employeeemergencycontact',
            name='employee',
        ),
        migrations.RemoveField(
            model_name='employeetraining',
            name='approved_by',
        ),
        migrations.RemoveField(
            model_name='employeetraining',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='employeetraining',
            name='employee',
        ),
        migrations.AlterUniqueTogether(
            name='payrollentry',
            unique_together={('payroll_period', 'employee')},
        ),
        migrations.RemoveField(
            model_name='payrollperiod',
            name='period',
        ),
        migrations.RemoveField(
            model_name='payrollperiod',
            name='total_amount',
        ),
        migrations.AlterField(
            model_name='payrollentry',
            name='id',
            field=models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف الفريد'),
        ),
        migrations.AlterField(
            model_name='payrollperiod',
            name='created_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.PROTECT, related_name='created_payroll_periods', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='payrollperiod',
            name='id',
            field=models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف'),
        ),
        migrations.AddIndex(
            model_name='payrollentry',
            index=models.Index(fields=['payroll_period', 'employee'], name='hrms_payrol_payroll_4e901d_idx'),
        ),
        migrations.AddIndex(
            model_name='payrollentry',
            index=models.Index(fields=['status'], name='hrms_payrol_status_77cc30_idx'),
        ),
        migrations.AddIndex(
            model_name='payrollentry',
            index=models.Index(fields=['payment_date'], name='hrms_payrol_payment_53b6ef_idx'),
        ),
        migrations.AddIndex(
            model_name='payrollperiod',
            index=models.Index(fields=['start_date', 'end_date'], name='Hr_payrollp_start_d_1ff43f_idx'),
        ),
        migrations.AddIndex(
            model_name='payrollperiod',
            index=models.Index(fields=['status'], name='Hr_payrollp_status_6601ac_idx'),
        ),
        migrations.AddIndex(
            model_name='payrollperiod',
            index=models.Index(fields=['period_type'], name='Hr_payrollp_period__2cc7ee_idx'),
        ),
        migrations.AddIndex(
            model_name='payrollperiod',
            index=models.Index(fields=['is_active'], name='Hr_payrollp_is_acti_3ea6db_idx'),
        ),
        migrations.AlterModelTable(
            name='payrollentry',
            table='hrms_payroll_entry',
        ),
        migrations.AlterModelTable(
            name='payrollperiod',
            table=None,
        ),
        migrations.AddField(
            model_name='employeesalarycomponent',
            name='salary_component',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_assignments', to='Hr.salarycomponent', verbose_name='مكون الراتب'),
        ),
        migrations.AddField(
            model_name='employeesalarycomponent',
            name='salary_structure',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='salary_components', to='Hr.employeesalarystructure', verbose_name='هيكل الراتب'),
        ),
        migrations.AddField(
            model_name='payrolldetail',
            name='adjusted_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='adjusted_payroll_details', to=settings.AUTH_USER_MODEL, verbose_name='عدل بواسطة'),
        ),
        migrations.AddField(
            model_name='payrolldetail',
            name='payroll_entry',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payroll_details', to='Hr.payrollentry', verbose_name='قيد الراتب'),
        ),
        migrations.AddField(
            model_name='payrolldetail',
            name='salary_component',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='payroll_details', to='Hr.salarycomponent', verbose_name='مكون الراتب'),
        ),
        migrations.AddField(
            model_name='payrolldetailhistory',
            name='changed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payroll_detail_changes', to=settings.AUTH_USER_MODEL, verbose_name='غير بواسطة'),
        ),
        migrations.AddField(
            model_name='payrolldetailhistory',
            name='payroll_detail',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='Hr.payrolldetail', verbose_name='تفاصيل الراتب'),
        ),
        migrations.DeleteModel(
            name='EmployeeEmergencyContact',
        ),
        migrations.DeleteModel(
            name='EmployeeTraining',
        ),
        migrations.RemoveField(
            model_name='payrollentry',
            name='period',
        ),
        migrations.RemoveField(
            model_name='payrollentry',
            name='total_amount',
        ),
        migrations.AddIndex(
            model_name='employeesalarycomponent',
            index=models.Index(fields=['salary_structure'], name='hrms_employ_salary__683f72_idx'),
        ),
        migrations.AddIndex(
            model_name='employeesalarycomponent',
            index=models.Index(fields=['salary_component'], name='hrms_employ_salary__a29c51_idx'),
        ),
        migrations.AddIndex(
            model_name='employeesalarycomponent',
            index=models.Index(fields=['is_active'], name='hrms_employ_is_acti_e3cee7_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='employeesalarycomponent',
            unique_together={('salary_structure', 'salary_component')},
        ),
        migrations.AddIndex(
            model_name='payrolldetail',
            index=models.Index(fields=['payroll_entry'], name='hrms_payrol_payroll_293638_idx'),
        ),
        migrations.AddIndex(
            model_name='payrolldetail',
            index=models.Index(fields=['salary_component'], name='hrms_payrol_salary__0465b5_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='payrolldetail',
            unique_together={('payroll_entry', 'salary_component')},
        ),
    ]
