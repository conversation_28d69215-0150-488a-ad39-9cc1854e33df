# Generated by Django 5.0.14 on 2025-07-19 17:50

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Hr', '0017_employeebank_employeecontact_employeeeducation_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='LegacyEmployee',
            fields=[
                ('emp_id', models.IntegerField(db_column='Emp_ID', primary_key=True, serialize=False, verbose_name='رقم الموظف')),
                ('emp_first_name', models.CharField(blank=True, db_column='Emp_First_Name', max_length=50, null=True, verbose_name='الاسم الأول')),
                ('emp_second_name', models.CharField(blank=True, db_column='Emp_Second_Name', max_length=50, null=True, verbose_name='الاسم الثاني')),
                ('emp_full_name', models.CharField(blank=True, db_column='Emp_Full_Name', max_length=100, null=True, verbose_name='الاسم الكامل')),
                ('working_condition', models.CharField(blank=True, choices=[('سارى', 'سارى'), ('إجازة', 'إجازة'), ('استقالة', 'استقالة'), ('انقطاع عن العمل', 'انقطاع عن العمل')], db_column='Working_Condition', max_length=50, null=True, verbose_name='حالة العمل')),
                ('insurance_status', models.CharField(blank=True, choices=[('مؤمن عليه', 'مؤمن عليه'), ('غير مؤمن عليه', 'غير مؤمن عليه')], db_column='Insurance_Status', max_length=50, null=True, verbose_name='حالة التأمين')),
                ('national_id', models.CharField(blank=True, db_column='National_ID', max_length=14, null=True, verbose_name='الرقم القومي')),
                ('date_birth', models.DateField(blank=True, db_column='Date_Birth', null=True, verbose_name='تاريخ الميلاد')),
                ('emp_date_hiring', models.DateField(blank=True, db_column='Emp_Date_Hiring', null=True, verbose_name='تاريخ التوظيف')),
                ('dept_name', models.CharField(blank=True, db_column='Dept_Name', max_length=50, null=True, verbose_name='اسم القسم')),
                ('jop_code', models.IntegerField(blank=True, db_column='Jop_Code', null=True, verbose_name='كود الوظيفة')),
            ],
            options={
                'verbose_name': 'الموظف',
                'verbose_name_plural': 'الموظفون',
                'db_table': 'Tbl_Employee',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='HrAttendanceMachine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الماكينة')),
                ('ip_address', models.CharField(max_length=15, verbose_name='عنوان IP')),
                ('port', models.PositiveIntegerField(default=4370, verbose_name='المنفذ')),
                ('machine_type', models.CharField(choices=[('in', 'حضور'), ('out', 'انصراف'), ('both', 'حضور وانصراف')], max_length=10, verbose_name='نوع الماكينة')),
                ('location', models.CharField(blank=True, max_length=100, null=True, verbose_name='الموقع')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'ماكينة الحضور',
                'verbose_name_plural': 'ماكينات الحضور',
                'db_table': 'Hr_AttendanceMachine',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrAttendanceRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('record_date', models.DateField(verbose_name='تاريخ التسجيل')),
                ('record_time', models.TimeField(verbose_name='وقت التسجيل')),
                ('record_type', models.CharField(choices=[('in', 'حضور'), ('out', 'انصراف')], max_length=5, verbose_name='نوع التسجيل')),
                ('source', models.CharField(choices=[('machine', 'ماكينة'), ('manual', 'يدوي')], default='machine', max_length=10, verbose_name='المصدر')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'سجل الحضور',
                'verbose_name_plural': 'سجلات الحضور',
                'db_table': 'Hr_AttendanceRecord',
                'ordering': ['record_date', 'record_time'],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrAttendanceRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القاعدة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف')),
                ('work_schedule', models.JSONField(verbose_name='جدول العمل')),
                ('late_grace_minutes', models.PositiveIntegerField(default=0, verbose_name='فترة سماح التأخير (دقائق)')),
                ('early_leave_grace_minutes', models.PositiveIntegerField(default=0, verbose_name='فترة سماح الانصراف المبكر (دقائق)')),
                ('weekly_off_days', models.JSONField(default=list, verbose_name='أيام الإجازة الأسبوعية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'قاعدة الحضور',
                'verbose_name_plural': 'قواعد الحضور',
                'db_table': 'Hr_AttendanceRule',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrAttendanceSummary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('status', models.CharField(choices=[('present', 'حاضر'), ('absent', 'غائب'), ('late', 'متأخر'), ('early_leave', 'انصراف مبكر'), ('holiday', 'إجازة'), ('weekend', 'عطلة أسبوعية')], max_length=20, verbose_name='الحالة')),
                ('time_in', models.TimeField(blank=True, null=True, verbose_name='وقت الحضور')),
                ('time_out', models.TimeField(blank=True, null=True, verbose_name='وقت الانصراف')),
                ('late_minutes', models.PositiveIntegerField(default=0, verbose_name='دقائق التأخير')),
                ('early_leave_minutes', models.PositiveIntegerField(default=0, verbose_name='دقائق الانصراف المبكر')),
                ('overtime_minutes', models.PositiveIntegerField(default=0, verbose_name='دقائق العمل الإضافي')),
                ('working_minutes', models.PositiveIntegerField(default=0, verbose_name='دقائق العمل')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'ملخص الحضور',
                'verbose_name_plural': 'ملخصات الحضور',
                'db_table': 'Hr_AttendanceSummary',
                'ordering': ['date'],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrCar',
            fields=[
                ('car_id', models.IntegerField(primary_key=True, serialize=False, verbose_name='رقم السيارة')),
                ('car_name', models.CharField(blank=True, max_length=50, null=True, verbose_name='اسم السيارة')),
                ('car_type', models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع السيارة')),
                ('car_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='تكلفة السيارة')),
                ('car_salary_farda', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='تكلفة السيارة (فردة)')),
                ('supplier', models.CharField(blank=True, max_length=50, null=True, verbose_name='المورد')),
                ('contract_type', models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع العقد')),
                ('car_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم السيارة')),
                ('car_license_expiration_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ انتهاء رخصة السيارة')),
                ('driver_name', models.CharField(blank=True, max_length=50, null=True, verbose_name='اسم السائق')),
                ('driver_phone', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم هاتف السائق')),
                ('driver_license_expiration_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ انتهاء رخصة السائق')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('shift_type', models.CharField(blank=True, choices=[('حضور فقط', 'حضور فقط'), ('انصراف فقط', 'انصراف فقط')], max_length=50, null=True, verbose_name='نوع الوردية')),
                ('contract_type_farada', models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع العقد (فردة)')),
            ],
            options={
                'verbose_name': 'السيارة',
                'verbose_name_plural': 'السيارات',
                'db_table': 'Hr_Car',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrEmployeeAttendanceRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('effective_date', models.DateField(verbose_name='تاريخ السريان')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'قاعدة حضور الموظف',
                'verbose_name_plural': 'قواعد حضور الموظفين',
                'db_table': 'Hr_EmployeeAttendanceRule',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrEmployeeNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الملاحظة')),
                ('content', models.TextField(help_text='اكتب تفاصيل الملاحظة هنا', verbose_name='محتوى الملاحظة')),
                ('note_type', models.CharField(choices=[('positive', 'إيجابية/جيدة'), ('negative', 'سلبية/ضعيفة'), ('general', 'عامة/محايدة')], default='general', max_length=20, verbose_name='نوع الملاحظة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=20, verbose_name='الأولوية')),
                ('evaluation_link', models.CharField(blank=True, help_text='رابط اختياري لربط الملاحظة بتقييم الأداء', max_length=500, null=True, verbose_name='رابط التقييم')),
                ('evaluation_score', models.DecimalField(blank=True, decimal_places=2, help_text='درجة التقييم المرتبطة بالملاحظة (اختياري)', max_digits=5, null=True, verbose_name='درجة التقييم')),
                ('is_important', models.BooleanField(default=False, verbose_name='ملاحظة مهمة')),
                ('is_confidential', models.BooleanField(default=False, help_text='ملاحظة سرية - محدودة الوصول', verbose_name='سرية')),
                ('is_active', models.BooleanField(default=True, help_text='إلغاء تفعيل الملاحظة بدلاً من حذفها', verbose_name='نشطة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('tags', models.CharField(blank=True, help_text='علامات مفصولة بفواصل للبحث والتصنيف', max_length=500, null=True, verbose_name='العلامات')),
                ('follow_up_required', models.BooleanField(default=False, verbose_name='يتطلب متابعة')),
                ('follow_up_date', models.DateField(blank=True, null=True, verbose_name='تاريخ المتابعة')),
            ],
            options={
                'verbose_name': 'ملاحظة الموظف',
                'verbose_name_plural': 'ملاحظات الموظفين',
                'ordering': ['-created_at'],
                'permissions': [('view_confidential_notes', 'يمكن عرض الملاحظات السرية'), ('manage_all_notes', 'يمكن إدارة جميع الملاحظات')],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrEmployeeNoteHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('created', 'تم الإنشاء'), ('updated', 'تم التحديث'), ('deleted', 'تم الحذف'), ('restored', 'تم الاستعادة')], max_length=20, verbose_name='الإجراء')),
                ('changed_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التغيير')),
                ('old_values', models.JSONField(blank=True, null=True, verbose_name='القيم السابقة')),
                ('new_values', models.JSONField(blank=True, null=True, verbose_name='القيم الجديدة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات التغيير')),
            ],
            options={
                'verbose_name': 'تاريخ ملاحظة الموظف',
                'verbose_name_plural': 'تاريخ ملاحظات الموظفين',
                'ordering': ['-changed_at'],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrEmployeeTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان المهمة')),
                ('description', models.TextField(verbose_name='وصف المهمة')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتملة'), ('cancelled', 'ملغاة')], default='pending', max_length=20, verbose_name='الحالة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=20, verbose_name='الأولوية')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('completion_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإنجاز')),
                ('progress', models.PositiveIntegerField(default=0, help_text='من 0 إلى 100', verbose_name='نسبة الإنجاز (%)')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مهمة الموظف',
                'verbose_name_plural': 'مهام الموظفين',
                'ordering': ['-due_date', 'priority'],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrOfficialHoliday',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الإجازة')),
                ('date', models.DateField(verbose_name='تاريخ الإجازة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف')),
                ('is_recurring', models.BooleanField(default=False, verbose_name='إجازة متكررة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إجازة رسمية',
                'verbose_name_plural': 'إجازات رسمية',
                'db_table': 'Hr_OfficialHoliday',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrPickupPoint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم النقطة')),
                ('address', models.CharField(max_length=255, verbose_name='العنوان')),
                ('coordinates', models.CharField(blank=True, max_length=100, null=True, verbose_name='الإحداثيات')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'نقطة تجمع',
                'verbose_name_plural': 'نقاط التجمع',
                'db_table': 'Hr_PickupPoint',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrTaskNew',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان المهمة')),
                ('description', models.TextField(verbose_name='وصف المهمة')),
                ('task_type', models.CharField(choices=[('insurance', 'متابعة التأمينات'), ('transportation', 'متابعة بدل المواصلات'), ('car_issues', 'مشاكل سيارات النقل'), ('contract_renewal', 'تجديد العقود'), ('other', 'أخرى')], default='other', max_length=20, verbose_name='نوع المهمة')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتملة'), ('cancelled', 'ملغاة')], default='pending', max_length=20, verbose_name='الحالة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=20, verbose_name='الأولوية')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('completion_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإنجاز')),
                ('progress', models.PositiveIntegerField(default=0, help_text='من 0 إلى 100', verbose_name='نسبة الإنجاز (%)')),
                ('steps_taken', models.TextField(blank=True, null=True, verbose_name='الخطوات المتخذة')),
                ('reminder_days', models.PositiveIntegerField(default=3, verbose_name='أيام التذكير قبل الموعد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مهمة الموارد البشرية',
                'verbose_name_plural': 'مهام الموارد البشرية',
                'ordering': ['-due_date', 'priority'],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='TaskStep',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_id', models.IntegerField(verbose_name='رقم المهمة')),
                ('description', models.TextField(verbose_name='وصف الخطوة')),
                ('completed', models.BooleanField(default=False, verbose_name='مكتملة')),
                ('completion_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإنجاز')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'خطوة المهمة',
                'verbose_name_plural': 'خطوات المهام',
                'ordering': ['-created_at'],
                'managed': True,
            },
        ),
        migrations.AlterModelOptions(
            name='department',
            options={'ordering': ['branch', 'level', 'name'], 'verbose_name': 'قسم', 'verbose_name_plural': 'الأقسام'},
        ),
        migrations.RemoveIndex(
            model_name='company',
            name='hrms_compan_name_e35078_idx',
        ),
        migrations.RemoveIndex(
            model_name='company',
            name='hrms_compan_tax_id_1a9057_idx',
        ),
        migrations.RemoveIndex(
            model_name='department',
            name='hrms_depart_company_73ee3e_idx',
        ),
        migrations.RemoveIndex(
            model_name='department',
            name='hrms_depart_code_c1d40b_idx',
        ),
        migrations.RenameField(
            model_name='department',
            old_name='budget',
            new_name='annual_budget',
        ),
        migrations.RenameField(
            model_name='department',
            old_name='parent_department',
            new_name='parent',
        ),
        migrations.RemoveField(
            model_name='company',
            name='currency',
        ),
        migrations.RemoveField(
            model_name='company',
            name='employee_count',
        ),
        migrations.RemoveField(
            model_name='company',
            name='hr_settings',
        ),
        migrations.RemoveField(
            model_name='company',
            name='leave_settings',
        ),
        migrations.RemoveField(
            model_name='company',
            name='legal_name',
        ),
        migrations.RemoveField(
            model_name='company',
            name='payroll_settings',
        ),
        migrations.RemoveField(
            model_name='company',
            name='primary_color',
        ),
        migrations.RemoveField(
            model_name='company',
            name='registration_number',
        ),
        migrations.RemoveField(
            model_name='company',
            name='secondary_color',
        ),
        migrations.RemoveField(
            model_name='company',
            name='tax_id',
        ),
        migrations.AlterUniqueTogether(
            name='department',
            unique_together={('branch', 'code')},
        ),
        migrations.AddField(
            model_name='company',
            name='business_type',
            field=models.CharField(choices=[('corporation', 'شركة مساهمة'), ('llc', 'شركة ذات مسؤولية محدودة'), ('partnership', 'شراكة'), ('sole_proprietorship', 'مؤسسة فردية'), ('government', 'جهة حكومية'), ('ngo', 'منظمة غير ربحية'), ('other', 'أخرى')], default='llc', max_length=30, verbose_name='نوع النشاط التجاري'),
        ),
        migrations.AddField(
            model_name='company',
            name='code',
            field=models.CharField(default=django.utils.timezone.now, max_length=20, unique=True, validators=[django.core.validators.RegexValidator(message='كود الشركة يجب أن يحتوي على أحرف كبيرة وأرقام فقط', regex='^[A-Z0-9\\-]+$')], verbose_name='كود الشركة'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='company',
            name='commercial_register',
            field=models.CharField(default=django.utils.timezone.now, max_length=50, unique=True, verbose_name='رقم السجل التجاري'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='company',
            name='default_currency',
            field=models.CharField(choices=[('EGP', 'جنيه مصري'), ('SAR', 'ريال سعودي'), ('AED', 'درهم إماراتي'), ('USD', 'دولار أمريكي'), ('EUR', 'يورو')], default='EGP', max_length=3, verbose_name='العملة الافتراضية'),
        ),
        migrations.AddField(
            model_name='company',
            name='fax',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الفاكس'),
        ),
        migrations.AddField(
            model_name='company',
            name='language',
            field=models.CharField(default='ar', max_length=10, verbose_name='اللغة الافتراضية'),
        ),
        migrations.AddField(
            model_name='company',
            name='name_english',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='الاسم بالإنجليزية'),
        ),
        migrations.AddField(
            model_name='company',
            name='state',
            field=models.CharField(default=django.utils.timezone.now, max_length=100, verbose_name='المحافظة/الولاية'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='company',
            name='tax_number',
            field=models.CharField(default=django.utils.timezone.now, max_length=50, unique=True, verbose_name='الرقم الضريبي'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='company',
            name='vat_number',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم ضريبة القيمة المضافة'),
        ),
        migrations.AddField(
            model_name='department',
            name='current_employees',
            field=models.PositiveIntegerField(default=0, verbose_name='العدد الحالي للموظفين'),
        ),
        migrations.AddField(
            model_name='department',
            name='is_profit_center',
            field=models.BooleanField(default=False, help_text='هل هذا القسم مركز ربح؟', verbose_name='مركز ربح'),
        ),
        migrations.AddField(
            model_name='department',
            name='location',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='الموقع'),
        ),
        migrations.AddField(
            model_name='department',
            name='max_employees',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='العدد الأقصى للموظفين'),
        ),
        migrations.AddField(
            model_name='department',
            name='name_english',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='الاسم بالإنجليزية'),
        ),
        migrations.AddField(
            model_name='department',
            name='objectives',
            field=models.JSONField(default=list, help_text='الأهداف الاستراتيجية للقسم', verbose_name='أهداف القسم'),
        ),
        migrations.AddField(
            model_name='department',
            name='working_hours',
            field=models.JSONField(default=dict, help_text='ساعات العمل الخاصة بالقسم', verbose_name='ساعات العمل'),
        ),
        migrations.AlterField(
            model_name='company',
            name='address',
            field=models.TextField(default=django.utils.timezone.now, verbose_name='العنوان'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='company',
            name='city',
            field=models.CharField(default=django.utils.timezone.now, max_length=100, verbose_name='المدينة'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='company',
            name='email',
            field=models.EmailField(default=django.utils.timezone.now, max_length=254, validators=[django.core.validators.EmailValidator()], verbose_name='البريد الإلكتروني'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='company',
            name='industry',
            field=models.CharField(default=django.utils.timezone.now, max_length=100, verbose_name='القطاع/الصناعة'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='company',
            name='name',
            field=models.CharField(max_length=200, verbose_name='اسم الشركة'),
        ),
        migrations.AlterField(
            model_name='company',
            name='phone',
            field=models.CharField(default=django.utils.timezone.now, max_length=20, validators=[django.core.validators.RegexValidator(message='رقم الهاتف يجب أن يكون صالحاً', regex='^\\+?1?\\d{9,15}$')], verbose_name='رقم الهاتف'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='department',
            name='branch',
            field=models.ForeignKey(default=django.utils.timezone.now, on_delete=django.db.models.deletion.CASCADE, related_name='departments', to='Hr.branch', verbose_name='الفرع'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='department',
            name='code',
            field=models.CharField(max_length=20, validators=[django.core.validators.RegexValidator(message='كود القسم يجب أن يحتوي على أحرف كبيرة وأرقام فقط', regex='^[A-Z0-9\\-]+$')], verbose_name='كود القسم'),
        ),
        migrations.AlterField(
            model_name='department',
            name='cost_center_code',
            field=models.CharField(blank=True, max_length=20, null=True, unique=True, verbose_name='كود مركز التكلفة'),
        ),
        migrations.AlterField(
            model_name='department',
            name='department_type',
            field=models.CharField(choices=[('administrative', 'إداري'), ('operational', 'تشغيلي'), ('support', 'دعم'), ('technical', 'تقني'), ('sales', 'مبيعات'), ('marketing', 'تسويق'), ('finance', 'مالي'), ('hr', 'موارد بشرية'), ('it', 'تكنولوجيا المعلومات'), ('legal', 'قانوني'), ('procurement', 'مشتريات'), ('quality', 'جودة'), ('research', 'بحث وتطوير'), ('production', 'إنتاج'), ('logistics', 'لوجستيات'), ('customer_service', 'خدمة العملاء'), ('other', 'أخرى')], default='administrative', max_length=20, verbose_name='نوع القسم'),
        ),
        migrations.AlterField(
            model_name='department',
            name='extension',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم التحويلة'),
        ),
        migrations.AlterField(
            model_name='department',
            name='floor',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='الطابق'),
        ),
        migrations.AlterField(
            model_name='department',
            name='level',
            field=models.PositiveIntegerField(default=0, help_text='مستوى القسم في الهيكل التنظيمي', verbose_name='مستوى القسم'),
        ),
        migrations.AlterField(
            model_name='payrollentry',
            name='basic_salary',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='الراتب الأساسي'),
        ),
        migrations.AlterField(
            model_name='payrollentry',
            name='employee',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='payroll_entries', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AlterField(
            model_name='payrollentry',
            name='payroll_period',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='payroll_entries', to='Hr.payrollperiod', verbose_name='فترة الراتب'),
        ),
        migrations.AlterField(
            model_name='payrollentry',
            name='salary_structure',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='payroll_entries', to='Hr.employeesalarystructure', verbose_name='هيكل الراتب'),
        ),
        migrations.AddIndex(
            model_name='company',
            index=models.Index(fields=['code'], name='hrms_compan_code_c1eeb4_idx'),
        ),
        migrations.AddIndex(
            model_name='company',
            index=models.Index(fields=['commercial_register'], name='hrms_compan_commerc_0199dc_idx'),
        ),
        migrations.AddIndex(
            model_name='company',
            index=models.Index(fields=['tax_number'], name='hrms_compan_tax_num_9ab43f_idx'),
        ),
        migrations.AddIndex(
            model_name='department',
            index=models.Index(fields=['branch', 'code'], name='hrms_depart_branch__25928b_idx'),
        ),
        migrations.AddIndex(
            model_name='department',
            index=models.Index(fields=['parent'], name='hrms_depart_parent__0a40cd_idx'),
        ),
        migrations.AddIndex(
            model_name='department',
            index=models.Index(fields=['cost_center_code'], name='hrms_depart_cost_ce_a7887e_idx'),
        ),
        migrations.AddField(
            model_name='hrattendancerecord',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='hr_attendance_records', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='hrattendancerecord',
            name='machine',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='records', to='Hr.HrAttendanceMachine', verbose_name='الماكينة'),
        ),
        migrations.AddField(
            model_name='hrattendancesummary',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='hr_attendance_summaries', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='hremployeeattendancerule',
            name='attendance_rule',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employees', to='Hr.HrAttendanceRule', verbose_name='قاعدة الحضور'),
        ),
        migrations.AddField(
            model_name='hremployeeattendancerule',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_rules', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='hremployeenote',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_employee_notes', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة'),
        ),
        migrations.AddField(
            model_name='hremployeenote',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_notes', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='hremployeenote',
            name='last_modified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modified_employee_notes', to=settings.AUTH_USER_MODEL, verbose_name='آخر تعديل بواسطة'),
        ),
        migrations.AddField(
            model_name='hremployeenotehistory',
            name='changed_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم التغيير بواسطة'),
        ),
        migrations.AddField(
            model_name='hremployeenotehistory',
            name='note',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='Hr.hremployeenote', verbose_name='الملاحظة'),
        ),
        migrations.AddField(
            model_name='hremployeetask',
            name='assigned_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='hr_assigned_tasks', to=settings.AUTH_USER_MODEL, verbose_name='تم التكليف بواسطة'),
        ),
        migrations.AddField(
            model_name='hremployeetask',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AlterUniqueTogether(
            name='hrofficialholiday',
            unique_together={('name', 'date')},
        ),
        migrations.AddField(
            model_name='hrpickuppoint',
            name='car',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pickup_points', to='Hr.hrcar', verbose_name='السيارة'),
        ),
        migrations.AddField(
            model_name='hrtasknew',
            name='assigned_to',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='hr_tasks', to=settings.AUTH_USER_MODEL, verbose_name='تم التكليف إلى'),
        ),
        migrations.AddField(
            model_name='hrtasknew',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_hr_tasks', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة'),
        ),
        migrations.AddField(
            model_name='taskstep',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_task_steps', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة'),
        ),
        migrations.RemoveField(
            model_name='department',
            name='annual_goals',
        ),
        migrations.RemoveField(
            model_name='department',
            name='company',
        ),
        migrations.RemoveField(
            model_name='department',
            name='email',
        ),
        migrations.RemoveField(
            model_name='department',
            name='employee_capacity',
        ),
        migrations.RemoveField(
            model_name='department',
            name='location_notes',
        ),
        migrations.RemoveField(
            model_name='department',
            name='phone',
        ),
        migrations.RemoveField(
            model_name='department',
            name='room_number',
        ),
        migrations.RemoveField(
            model_name='department',
            name='working_hours_end',
        ),
        migrations.RemoveField(
            model_name='department',
            name='working_hours_start',
        ),
        migrations.AlterUniqueTogether(
            name='hrattendancesummary',
            unique_together={('employee', 'date')},
        ),
    ]
