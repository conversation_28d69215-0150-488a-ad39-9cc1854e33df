#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ElDawliya_sys.settings')
django.setup()

from django.db import connection

def check_emergency_contact_table():
    """Check the EmployeeEmergencyContact table schema and constraints"""
    with connection.cursor() as cursor:
        # Check if problematic tables exist
        problematic_tables = [
            'hrms_employee_emergency_contact',
            'Hr_AttendanceSummary',
            'hrms_hr_attendance_summary',
            'Hr_PickupPoint',
            'hrms_hr_pickup_point'
        ]

        for table_name in problematic_tables:
            cursor.execute("""
                SELECT TABLE_NAME
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_NAME = %s
            """, [table_name])
            table_exists = cursor.fetchone()
            print(f'Table {table_name} exists: {table_exists is not None}')

        
        # Also check if the table was deleted/renamed
        cursor.execute("""
            SELECT TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_NAME LIKE '%emergency%' OR TABLE_NAME LIKE '%contact%'
        """)
        similar_tables = cursor.fetchall()
        print(f'\nTables with "emergency" or "contact" in name:')
        for table in similar_tables:
            print(f'  - {table[0]}')

        # Check all Hr app tables
        cursor.execute("""
            SELECT TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_NAME LIKE 'hrms_%' OR TABLE_NAME LIKE 'Hr_%'
            ORDER BY TABLE_NAME
        """)
        hr_tables = cursor.fetchall()
        print(f'\nAll HR-related tables:')
        for table in hr_tables:
            print(f'  - {table[0]}')

if __name__ == '__main__':
    check_emergency_contact_table()
