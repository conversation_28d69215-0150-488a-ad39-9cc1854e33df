#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ElDawliya_sys.settings')
django.setup()

from django.db import connection

def check_payroll_tables():
    """Check PayrollEntry and PayrollPeriod table schemas"""
    with connection.cursor() as cursor:
        # Check PayrollEntry table structure
        payroll_tables = ['Hr_PayrollEntry', 'hrms_payroll_entry', 'Hr_PayrollPeriod']

        for table_name in payroll_tables:
            cursor.execute("""
                SELECT TABLE_NAME
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_NAME = %s
            """, [table_name])
            table_exists = cursor.fetchone()
            print(f'Table {table_name} exists: {table_exists is not None}')

            if table_exists:
                # Check columns
                cursor.execute("""
                    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = %s
                    ORDER BY ORDINAL_POSITION
                """, [table_name])
                columns = cursor.fetchall()
                print(f'  Columns in {table_name}:')
                for col in columns:
                    print(f'    - {col[0]} ({col[1]}, nullable: {col[2]})')
                print()

        
        # Also check if the table was deleted/renamed
        cursor.execute("""
            SELECT TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_NAME LIKE '%emergency%' OR TABLE_NAME LIKE '%contact%'
        """)
        similar_tables = cursor.fetchall()
        print(f'\nTables with "emergency" or "contact" in name:')
        for table in similar_tables:
            print(f'  - {table[0]}')

        # Check all Hr app tables
        cursor.execute("""
            SELECT TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_NAME LIKE 'hrms_%' OR TABLE_NAME LIKE 'Hr_%'
            ORDER BY TABLE_NAME
        """)
        hr_tables = cursor.fetchall()
        print(f'\nAll HR-related tables:')
        for table in hr_tables:
            print(f'  - {table[0]}')

if __name__ == '__main__':
    check_payroll_tables()
