# Generated by Django 5.0.14 on 2025-07-20 14:54

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Hr', '0020_employeesalarycomponent_payrolldetail_and_more'),
    ]

    operations = [
        # COMMENTED OUT - employeetraining table doesn't exist in database
        # migrations.RemoveField(
        #     model_name='employeetraining',
        #     name='approved_by',
        # ),
        # migrations.RemoveField(
        #     model_name='employeetraining',
        #     name='created_by',
        # ),
        # migrations.RemoveField(
        #     model_name='employeetraining',
        #     name='employee',
        # ),
        # COMMENTED OUT - MSSQL doesn't support altering BigAutoField to UUIDField
        # migrations.AlterField(
        #     model_name='payrollentry',
        #     name='id',
        #     field=models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف الفريد'),
        # ),
        # migrations.<PERSON>er<PERSON>ield(
        #     model_name='payrollperiod',
        #     name='id',
        #     field=models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='المعرف'),
        # ),
        # COMMENTED OUT - Table naming conflicts and potential issues
        # migrations.AlterModelTable(
        #     name='payrollentry',
        #     table='hrms_payroll_entry',
        # ),
        # migrations.AlterModelTable(
        #     name='payrollperiod',
        #     table=None,
        # ),
        # COMMENTED OUT - Models don't exist in database
        # migrations.DeleteModel(
        #     name='EmployeeEmergencyContact',
        # ),
        # migrations.DeleteModel(
        #     name='EmployeeTraining',
        # ),
    ]
