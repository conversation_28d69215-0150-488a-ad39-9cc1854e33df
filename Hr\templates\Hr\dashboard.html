{% extends 'Hr/base_hr_modern.html' %}

{% block title %}لوحة تحكم الموارد البشرية - نظام الدولية{% endblock %}

{% block page_title %}لوحة تحكم الموارد البشرية{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item active">لوحة تحكم الموارد البشرية</li>
{% endblock %}

{% block content %}
<!-- Quick Access Toolbar -->
<div class="section-spacing">
    <div class="card">
        <div class="card-body">
            <div class="flex flex-wrap justify-between items-center gap-4">
                <div class="flex flex-wrap gap-2">
                    <a href="{% url 'Hr:employees:create' %}" class="btn btn-primary btn-with-icon">
                        <i class="fas fa-user-plus"></i>
                        <span>إضافة موظف</span>
                    </a>
                    <a href="#" class="btn btn-outline-info btn-with-icon" onclick="alert('نظام الحضور غير متاح حالياً')">
                        <i class="fas fa-clipboard-check"></i>
                        <span>سجل الحضور</span>
                    </a>
                    <a href="{% url 'Hr:employees:employee_search' %}" class="btn btn-outline-success btn-with-icon">
                        <i class="fas fa-search"></i>
                        <span>البحث المتقدم</span>
                    </a>
                </div>
                <div class="input-group max-w-sm">
                    <input type="text" class="form-control" id="quickSearch" placeholder="بحث عن موظف..." aria-label="بحث عن موظف">
                    <button class="btn btn-outline-secondary" type="button" id="quickSearchBtn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Top Stats Cards Section -->
<div class="section-spacing">
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
        <a href="{% url 'Hr:employees:list' %}" class="card card-stats card-hover">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-primary-light rounded-full flex items-center justify-center me-3">
                            <i class="fas fa-users text-primary text-xl"></i>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-primary">{{ total_employees }}</div>
                            <p class="text-sm text-muted mb-0">إجمالي الموظفين</p>
                        </div>
                    </div>
                    <div class="badge badge-primary">الكل</div>
                </div>
            </div>
        </a>

        <a href="{% url 'Hr:employees:list' %}?status=active" class="card card-stats card-hover">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-success-light rounded-full flex items-center justify-center me-3">
                            <i class="fas fa-user-check text-success text-xl"></i>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-success">{{ active_employees }}</div>
                            <p class="text-sm text-muted mb-0">الموظفين النشطين</p>
                        </div>
                    </div>
                    <div class="badge badge-success">نشط</div>
                </div>
            </div>
        </a>

        <a href="{% url 'Hr:departments:department_list' %}" class="card card-stats card-hover">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-info-light rounded-full flex items-center justify-center me-3">
                            <i class="fas fa-building text-info text-xl"></i>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-info">{{ departments_count }}</div>
                            <p class="text-sm text-muted mb-0">الأقسام</p>
                        </div>
                    </div>
                    <i class="fas fa-chevron-left text-muted"></i>
                </div>
            </div>
        </a>

        <a href="{% url 'Hr:jobs:job_list' %}" class="card card-stats card-hover">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-warning-light rounded-full flex items-center justify-center me-3">
                            <i class="fas fa-briefcase text-warning text-xl"></i>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-warning">{{ jobs_count }}</div>
                            <p class="text-sm text-muted mb-0">الوظائف</p>
                        </div>
                    </div>
                    <i class="fas fa-chevron-left text-muted"></i>
                </div>
            </div>
        </a>
    </div>
</div>

<!-- Main Content Section -->
<div class="section-spacing">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column -->
        <div class="lg:col-span-2">
            <div class="card">
                <div class="card-header">
                    <div class="card-header-content">
                        <div class="card-title">
                            <i class="fas fa-users"></i>
                            <span>إدارة الموظفين</span>
                        </div>
                        <div class="card-subtitle">العمليات الأساسية لإدارة الموظفين</div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <a href="{% url 'Hr:employees:list' %}" class="card card-hover">
                            <div class="card-body">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-primary-light rounded-full flex items-center justify-center me-3">
                                        <i class="fas fa-users text-primary text-xl"></i>
                                    </div>
                                    <div>
                                        <h6 class="font-medium mb-1">قائمة الموظفين</h6>
                                        <p class="text-sm text-muted mb-0">عرض وإدارة بيانات الموظفين</p>
                                    </div>
                                </div>
                            </div>
                        </a>

                        <a href="{% url 'Hr:employees:create' %}" class="card card-hover">
                            <div class="card-body">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-success-light rounded-full flex items-center justify-center me-3">
                                        <i class="fas fa-user-plus text-success text-xl"></i>
                                    </div>
                                    <div>
                                        <h6 class="font-medium mb-1">إضافة موظف جديد</h6>
                                        <p class="text-sm text-muted mb-0">تسجيل بيانات موظف جديد</p>
                                    </div>
                                </div>
                            </div>
                        </a>

                        <a href="{% url 'Hr:departments:department_list' %}" class="card card-hover">
                            <div class="card-body">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-info-light rounded-full flex items-center justify-center me-3">
                                        <i class="fas fa-building text-info text-xl"></i>
                                    </div>
                                    <div>
                                        <h6 class="font-medium mb-1">الأقسام</h6>
                                        <p class="text-sm text-muted mb-0">عرض وإدارة الأقسام</p>
                                    </div>
                                </div>
                            </div>
                        </a>

                        <a href="{% url 'Hr:jobs:job_list' %}" class="card card-hover">
                            <div class="card-body">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-warning-light rounded-full flex items-center justify-center me-3">
                                        <i class="fas fa-briefcase text-warning text-xl"></i>
                                    </div>
                                    <div>
                                        <h6 class="font-medium mb-1">الوظائف</h6>
                                        <p class="text-sm text-muted mb-0">عرض وإدارة المسميات الوظيفية</p>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="space-y-6">
            <!-- Attendance Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-header-content">
                        <div class="card-title">
                            <i class="fas fa-clock"></i>
                            <span>الحضور والانصراف</span>
                        </div>
                        <div class="card-subtitle">إدارة الحضور والانصراف</div>
                    </div>
                </div>
                <div class="card-body">
                    <a href="#" class="card card-hover" onclick="alert('نظام الحضور غير متاح حالياً')">
                        <div class="card-body">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-primary-light rounded-full flex items-center justify-center me-3">
                                    <i class="fas fa-clipboard-list text-primary text-xl"></i>
                                </div>
                                <div>
                                    <h6 class="font-medium mb-1">سجلات الحضور</h6>
                                    <p class="text-sm text-muted mb-0">عرض وإدارة سجلات الحضور</p>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="card">
                <div class="card-header">
                    <div class="card-header-content">
                        <div class="card-title">
                            <i class="fas fa-history"></i>
                            <span>النشاطات الأخيرة</span>
                        </div>
                        <div class="card-subtitle">آخر العمليات المنجزة</div>
                    </div>
                </div>
                <div class="card-body">
                    {% if recent_employees %}
                        <div class="space-y-3">
                            {% for employee in recent_employees|slice:":5" %}
                            <div class="flex items-center p-3 bg-light rounded-lg">
                                <div class="w-10 h-10 bg-success-light rounded-full flex items-center justify-center me-3">
                                    <i class="fas fa-user-plus text-success"></i>
                                </div>
                                <div class="flex-1">
                                    <h6 class="font-medium mb-1">تم إضافة موظف جديد</h6>
                                    <p class="text-sm text-muted mb-1">
                                        {{ employee.emp_full_name }} - {{ employee.department.dept_name|default:"بدون قسم" }}
                                    </p>
                                    <small class="text-muted">{{ employee.emp_date_hiring|timesince }} مضت</small>
                                </div>
                                <a href="{% url 'Hr:employees:detail' employee.emp_id %}" class="btn btn-sm btn-outline-primary">
                                    عرض
                                </a>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="empty-state-content">
                                <h6 class="empty-state-title">لا توجد أنشطة حديثة</h6>
                                <p class="empty-state-description">ستظهر هنا آخر العمليات المنجزة</p>
                            </div>
                        </div>
                    {% endif %}

                    <div class="text-center mt-4">
                        <a href="{% url 'Hr:employees:list' %}" class="btn btn-outline-primary btn-sm">
                            عرض جميع الموظفين
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats Row -->
<div class="section-spacing">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="card card-gradient-primary text-white">
            <div class="card-body text-center">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-calendar-check text-white text-2xl"></i>
                </div>
                <div class="text-3xl font-bold mb-2">{{ today_attendance|default:"0" }}</div>
                <p class="text-white text-opacity-90 mb-0">حضور اليوم</p>
            </div>
        </div>

        <div class="card card-gradient-success text-white">
            <div class="card-body text-center">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-user-shield text-white text-2xl"></i>
                </div>
                <div class="text-3xl font-bold mb-2">{{ insured_employees|default:"0" }}</div>
                <p class="text-white text-opacity-90 mb-0">مؤمن عليهم</p>
            </div>
        </div>

        <div class="card card-gradient-warning text-white">
            <div class="card-body text-center">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-calendar-times text-white text-2xl"></i>
                </div>
                <div class="text-3xl font-bold mb-2">{{ on_leave_today|default:"0" }}</div>
                <p class="text-white text-opacity-90 mb-0">في إجازة اليوم</p>
            </div>
        </div>

        <div class="card card-gradient-info text-white">
            <div class="card-body text-center">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-birthday-cake text-white text-2xl"></i>
                </div>
                <div class="text-3xl font-bold mb-2">{{ birthdays_today|default:"0" }}</div>
                <p class="text-white text-opacity-90 mb-0">أعياد ميلاد اليوم</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Quick Search Functionality
        const quickSearch = document.getElementById('quickSearch');
        const quickSearchBtn = document.getElementById('quickSearchBtn');
        
        if (quickSearch && quickSearchBtn) {
            // Handle search button click
            quickSearchBtn.addEventListener('click', function() {
                handleSearch();
            });
            
            // Handle Enter key press in search input
            quickSearch.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleSearch();
                }
            });
            
            function handleSearch() {
                const searchTerm = quickSearch.value.trim();
                if (searchTerm) {
                    // Redirect to employee search with the search term
                    window.location.href = "{% url 'Hr:employees:list' %}?search=" + encodeURIComponent(searchTerm);
                }
            }
        }
        
        // Interactive Card Effects
        const actionCards = document.querySelectorAll('.action-card');
        actionCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 10px 20px rgba(0,0,0,0.1)';
                
                const icon = this.querySelector('.action-icon');
                if (icon) {
                    icon.classList.add('pulse');
                    
                    // Remove the animation class after it completes
                    icon.addEventListener('animationend', function() {
                        icon.classList.remove('pulse');
                    }, { once: true });
                }
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 4px 10px rgba(0,0,0,0.05)';
            });
        });
    });
</script>
{% endblock %}
